@echo off
title Roop Floyd Face Swap Launcher

REM Change to the directory where the batch file is located
cd /D "%~dp0"

echo ================================================================
echo   Roop Floyd Face Swap v4.4.1
echo   AI-Powered Face Swapping Application
echo ================================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH!
    echo Please install Python 3.9 or higher from https://python.org
    echo.
    pause
    exit /b 1
)

REM Display Python version
echo Python version:
python --version
echo.

REM Check if virtual environment exists
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo Failed to create virtual environment!
        pause
        exit /b 1
    )
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Install/update dependencies
echo Checking dependencies...
pip install -r requirements.txt --quiet

REM Launch the application
echo.
echo Starting Roop Floyd Face Swap...
echo The web interface will open in your browser.
echo Close this window to stop the application.
echo.

python launcher.py

REM Deactivate virtual environment
deactivate

echo.
echo Application closed.
pause
