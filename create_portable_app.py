#!/usr/bin/env python3
"""
Create Portable App Distribution for Roop Floyd Face Swap
This script creates a self-contained portable application
"""

import os
import sys
import shutil
import subprocess
import zipfile
from pathlib import Path

APP_NAME = "RoopFloyd_Portable"
EXCLUDE_PATTERNS = [
    '__pycache__',
    '*.pyc',
    '*.pyo',
    '.git',
    '.gitignore',
    'venv',
    'env',
    '.env',
    'temp',
    'output',
    '*.log',
    '.DS_Store',
    'Thumbs.db'
]

def create_directory_structure():
    """Create the portable app directory structure"""
    print("Creating directory structure...")
    
    # Create main app directory
    app_dir = Path(APP_NAME)
    if app_dir.exists():
        shutil.rmtree(app_dir)
    
    app_dir.mkdir()
    
    # Create subdirectories
    (app_dir / "app").mkdir()
    (app_dir / "models").mkdir()
    (app_dir / "output").mkdir()
    (app_dir / "temp").mkdir()
    
    return app_dir

def copy_source_files(app_dir):
    """Copy source files to portable app directory"""
    print("Copying source files...")
    
    source_files = [
        "roop",
        "ui", 
        "clip",
        "run.py",
        "launcher.py",
        "settings.py",
        "config.yaml",
        "requirements.txt"
    ]
    
    app_source_dir = app_dir / "app"
    
    for item in source_files:
        src_path = Path(item)
        if src_path.exists():
            if src_path.is_dir():
                shutil.copytree(src_path, app_source_dir / item, 
                              ignore=shutil.ignore_patterns(*EXCLUDE_PATTERNS))
            else:
                shutil.copy2(src_path, app_source_dir / item)
        else:
            print(f"Warning: {item} not found, skipping...")

def create_launchers(app_dir):
    """Create launcher scripts for the portable app"""
    print("Creating launcher scripts...")
    
    # Windows batch launcher
    batch_content = f'''@echo off
title {APP_NAME}
cd /D "%~dp0\\app"
python launcher.py
pause
'''
    
    with open(app_dir / "Start_RoopFloyd.bat", "w") as f:
        f.write(batch_content)
    
    # PowerShell launcher
    ps_content = f'''# {APP_NAME} PowerShell Launcher
Set-Location "$PSScriptRoot\\app"
python launcher.py
Read-Host "Press Enter to exit"
'''
    
    with open(app_dir / "Start_RoopFloyd.ps1", "w") as f:
        f.write(ps_content)
    
    # Linux/Mac shell script
    shell_content = f'''#!/bin/bash
cd "$(dirname "$0")/app"
python3 launcher.py
read -p "Press Enter to exit..."
'''
    
    shell_script = app_dir / "start_roopfloyd.sh"
    with open(shell_script, "w") as f:
        f.write(shell_content)
    
    # Make shell script executable
    shell_script.chmod(0o755)

def create_readme(app_dir):
    """Create README file for the portable app"""
    print("Creating README...")
    
    readme_content = f"""# {APP_NAME}

## AI-Powered Face Swapping Application

### Quick Start:
1. **Windows**: Double-click `Start_RoopFloyd.bat`
2. **Linux/Mac**: Run `./start_roopfloyd.sh`

### Requirements:
- Python 3.9 or higher
- Internet connection (for first-time model downloads)

### First Run:
- The application will automatically install required dependencies
- AI models will be downloaded on first use (~500MB)
- A web interface will open in your browser

### Folders:
- `app/` - Application source code
- `models/` - AI models (downloaded automatically)
- `output/` - Generated images/videos
- `temp/` - Temporary files

### Troubleshooting:
- Ensure Python is installed and in your system PATH
- For video processing, install FFmpeg
- Check firewall settings if the web interface doesn't open

### Support:
- Check the console output for error messages
- Ensure you have sufficient disk space (2GB recommended)

Version: 4.4.1
"""
    
    with open(app_dir / "README.txt", "w") as f:
        f.write(readme_content)

def create_requirements_file(app_dir):
    """Create a simplified requirements file for portable distribution"""
    print("Creating portable requirements file...")
    
    # Create a CPU-only requirements file for better compatibility
    portable_requirements = """# Roop Floyd Portable Requirements
# CPU-only versions for better compatibility
numpy==1.26.4
gradio==5.9.1
opencv-python-headless==*********
onnx==1.16.1
insightface==0.7.3
psutil==5.9.6
torch==2.5.1
torchvision==0.20.1
onnxruntime==1.20.1
tqdm==4.66.4
ftfy
regex
pyvirtualcam
PyYAML
"""
    
    with open(app_dir / "app" / "requirements_portable.txt", "w") as f:
        f.write(portable_requirements)

def create_zip_distribution(app_dir):
    """Create a ZIP file of the portable app"""
    print("Creating ZIP distribution...")
    
    zip_filename = f"{APP_NAME}_v4.4.1.zip"
    
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(app_dir):
            for file in files:
                file_path = Path(root) / file
                arc_path = file_path.relative_to(app_dir.parent)
                zipf.write(file_path, arc_path)
    
    print(f"Created: {zip_filename}")
    return zip_filename

def main():
    """Main function to create portable app"""
    print("=" * 60)
    print("  Roop Floyd Portable App Creator")
    print("=" * 60)
    print()
    
    try:
        # Create directory structure
        app_dir = create_directory_structure()
        
        # Copy source files
        copy_source_files(app_dir)
        
        # Create launchers
        create_launchers(app_dir)
        
        # Create README
        create_readme(app_dir)
        
        # Create portable requirements
        create_requirements_file(app_dir)
        
        # Create ZIP distribution
        zip_file = create_zip_distribution(app_dir)
        
        print()
        print("=" * 60)
        print("  Portable App Creation Complete!")
        print("=" * 60)
        print(f"Portable app folder: {app_dir}")
        print(f"ZIP distribution: {zip_file}")
        print()
        print("To distribute:")
        print(f"1. Share the {zip_file} file")
        print("2. Users extract and run the launcher script")
        print("3. Dependencies will be installed automatically")
        
    except Exception as e:
        print(f"Error creating portable app: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
