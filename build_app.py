#!/usr/bin/env python3
"""
Build Script for Roop Floyd FS Application
Creates both portable and installable versions
"""

import os
import sys
import subprocess
import shutil
import argparse
from pathlib import Path

def run_command(cmd, check=True):
    """Run a command and return the result"""
    print(f"Running: {cmd}")
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    if check and result.returncode != 0:
        print(f"Error: {result.stderr}")
        return False
    return True

def check_dependencies():
    """Check if required build tools are available"""
    print("Checking build dependencies...")
    
    # Check Python
    if not run_command("python --version", check=False):
        print("Error: Python not found!")
        return False
    
    # Check if we can import required modules
    try:
        import zipfile
        import shutil
    except ImportError as e:
        print(f"Error: Missing Python module: {e}")
        return False
    
    print("✓ Build dependencies OK")
    return True

def clean_build_artifacts():
    """Clean previous build artifacts"""
    print("Cleaning previous builds...")
    
    artifacts = [
        "RoopFloyd_Portable",
        "RoopFloyd_Portable_*.zip",
        "RoopFloyd_Setup_*.exe",
        "dist",
        "build",
        "__pycache__"
    ]
    
    for pattern in artifacts:
        for path in Path(".").glob(pattern):
            if path.is_dir():
                shutil.rmtree(path)
            else:
                path.unlink()
    
    print("✓ Cleaned build artifacts")

def build_portable():
    """Build portable version"""
    print("\n" + "="*50)
    print("Building Portable Version")
    print("="*50)
    
    if not run_command("python create_portable_app.py"):
        print("Failed to create portable app!")
        return False
    
    print("✓ Portable version created successfully")
    return True

def build_installer():
    """Build Windows installer using NSIS"""
    print("\n" + "="*50)
    print("Building Windows Installer")
    print("="*50)
    
    # Check if NSIS is available
    nsis_paths = [
        r"C:\Program Files (x86)\NSIS\makensis.exe",
        r"C:\Program Files\NSIS\makensis.exe",
        "makensis.exe"  # If in PATH
    ]
    
    nsis_exe = None
    for path in nsis_paths:
        if shutil.which(path) or Path(path).exists():
            nsis_exe = path
            break
    
    if not nsis_exe:
        print("Warning: NSIS not found. Skipping installer creation.")
        print("To create Windows installer:")
        print("1. Install NSIS from https://nsis.sourceforge.io/")
        print("2. Run: makensis installer.nsi")
        return False
    
    # Create installer
    if not run_command(f'"{nsis_exe}" installer.nsi'):
        print("Failed to create installer!")
        return False
    
    print("✓ Windows installer created successfully")
    return True

def create_pyinstaller_executable():
    """Create standalone executable using PyInstaller"""
    print("\n" + "="*50)
    print("Building Standalone Executable")
    print("="*50)
    
    # Check if PyInstaller is available
    try:
        import PyInstaller
    except ImportError:
        print("Installing PyInstaller...")
        if not run_command("pip install pyinstaller"):
            print("Failed to install PyInstaller!")
            return False
    
    # Create spec file for PyInstaller
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['launcher.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('roop', 'roop'),
        ('ui', 'ui'),
        ('clip', 'clip'),
        ('config.yaml', '.'),
        ('requirements.txt', '.'),
    ],
    hiddenimports=[
        'gradio',
        'torch',
        'onnxruntime',
        'cv2',
        'insightface',
        'numpy',
        'PIL',
        'yaml'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='RoopFloyd',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='RoopFloyd',
)
'''
    
    with open("roopfloyd.spec", "w") as f:
        f.write(spec_content)
    
    # Build executable
    if not run_command("pyinstaller roopfloyd.spec --clean"):
        print("Failed to create executable!")
        return False
    
    print("✓ Standalone executable created in dist/RoopFloyd/")
    return True

def main():
    """Main build function"""
    parser = argparse.ArgumentParser(description="Build Roop Floyd FS Application")
    parser.add_argument("--portable", action="store_true", help="Build portable version only")
    parser.add_argument("--installer", action="store_true", help="Build installer only")
    parser.add_argument("--executable", action="store_true", help="Build standalone executable only")
    parser.add_argument("--clean", action="store_true", help="Clean build artifacts only")
    parser.add_argument("--all", action="store_true", help="Build all versions (default)")
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("  Roop Floyd FS - Build Script")
    print("=" * 60)
    
    # Clean if requested
    if args.clean:
        clean_build_artifacts()
        return 0
    
    # Check dependencies
    if not check_dependencies():
        return 1
    
    # Clean previous builds
    clean_build_artifacts()
    
    success = True
    
    # Determine what to build
    build_all = args.all or not (args.portable or args.installer or args.executable)
    
    if args.portable or build_all:
        success &= build_portable()
    
    if args.installer or build_all:
        success &= build_installer()
    
    if args.executable or build_all:
        success &= create_pyinstaller_executable()
    
    print("\n" + "="*60)
    if success:
        print("  Build completed successfully!")
        print("="*60)
        print("\nGenerated files:")
        
        # List generated files
        for pattern in ["RoopFloyd_Portable_*.zip", "RoopFloyd_Setup_*.exe"]:
            for file in Path(".").glob(pattern):
                print(f"  - {file}")
        
        if Path("dist/RoopFloyd").exists():
            print("  - dist/RoopFloyd/ (standalone executable)")
        
    else:
        print("  Build completed with errors!")
        print("="*60)
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
