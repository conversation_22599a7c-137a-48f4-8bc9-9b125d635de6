#!/usr/bin/env python3
"""
<PERSON><PERSON> Face Swap Application Launcher
Handles environment setup, dependency checking, and application startup
"""

import os
import sys
import subprocess
import platform
import shutil
from pathlib import Path

# Application metadata
APP_NAME = "Roop <PERSON> Face Swap"
APP_VERSION = "4.4.1"
REQUIRED_PYTHON_VERSION = (3, 9)

def check_python_version():
    """Check if Python version meets requirements"""
    if sys.version_info < REQUIRED_PYTHON_VERSION:
        print(f"Error: Python {REQUIRED_PYTHON_VERSION[0]}.{REQUIRED_PYTHON_VERSION[1]}+ is required.")
        print(f"Current version: {sys.version}")
        return False
    return True

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'gradio', 'torch', 'onnxruntime', 'opencv-python',
        'insightface', 'numpy', 'psutil', 'tqdm'
    ]

    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)

    return missing_packages

def install_dependencies():
    """Install missing dependencies"""
    print("Installing dependencies...")
    try:
        # Check if we have a portable requirements file
        req_file = 'requirements_portable.txt' if os.path.exists('requirements_portable.txt') else 'requirements.txt'

        print(f"Using requirements file: {req_file}")

        # Install requirements
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', '-r', req_file, '--upgrade'
        ])
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error installing dependencies: {e}")
        print("Trying CPU-only installation...")

        # Try CPU-only installation
        try:
            cpu_packages = [
                'numpy==1.26.4',
                'gradio==5.9.1',
                'opencv-python-headless==*********',
                'onnx==1.16.1',
                'psutil==5.9.6',
                'torch==2.5.1',
                'torchvision==0.20.1',
                'onnxruntime==1.20.1',
                'tqdm==4.66.4',
                'ftfy',
                'regex',
                'PyYAML'
            ]

            for package in cpu_packages:
                subprocess.check_call([
                    sys.executable, '-m', 'pip', 'install', package
                ])

            print("CPU-only installation completed successfully!")
            return True
        except subprocess.CalledProcessError as e2:
            print(f"CPU-only installation also failed: {e2}")
            return False

def check_ffmpeg():
    """Check if FFmpeg is available"""
    return shutil.which('ffmpeg') is not None

def setup_environment():
    """Setup application environment"""
    # Create necessary directories
    os.makedirs('output', exist_ok=True)
    os.makedirs('temp', exist_ok=True)
    os.makedirs('models', exist_ok=True)

    # Set environment variables
    os.environ['GRADIO_ANALYTICS_ENABLED'] = '0'
    if not os.environ.get('TEMP'):
        os.environ['TEMP'] = os.path.abspath('temp')
        os.environ['TMP'] = os.path.abspath('temp')

def print_banner():
    """Print application banner"""
    print("=" * 60)
    print(f"  {APP_NAME} v{APP_VERSION}")
    print("  AI-Powered Face Swapping Application")
    print("=" * 60)
    print()

def main():
    """Main launcher function"""
    print_banner()

    # Check Python version
    if not check_python_version():
        input("Press Enter to exit...")
        sys.exit(1)

    # Setup environment
    setup_environment()

    # Check dependencies
    missing_deps = check_dependencies()
    if missing_deps:
        print(f"Missing dependencies: {', '.join(missing_deps)}")
        response = input("Install missing dependencies? (y/n): ").lower()
        if response == 'y':
            if not install_dependencies():
                print("Failed to install dependencies!")
                input("Press Enter to exit...")
                sys.exit(1)
        else:
            print("Cannot run without required dependencies!")
            input("Press Enter to exit...")
            sys.exit(1)

    # Check FFmpeg
    if not check_ffmpeg():
        print("Warning: FFmpeg not found. Video processing will be limited.")
        print("Please install FFmpeg for full functionality.")
        print()

    # Launch application
    print("Starting Roop Floyd Face Swap...")
    print("The web interface will open in your browser.")
    print("Close this window to stop the application.")
    print()

    try:
        # Import and run the main application
        from roop import core
        core.run()
    except KeyboardInterrupt:
        print("\nApplication stopped by user.")
    except Exception as e:
        print(f"Error starting application: {e}")
        input("Press Enter to exit...")
        sys.exit(1)

if __name__ == '__main__':
    main()
