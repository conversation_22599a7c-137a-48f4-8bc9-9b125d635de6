# 🎭 Roop Floyd FS - User Guide

## 📦 What You Have

You now have a **lightweight portable application** (`RoopFloyd_Portable_v4.4.1.zip` - only **0.09 MB**!) that performs AI-powered face swapping.

## 🚀 Quick Start

### For End Users:

1. **Download & Extract**
   - Extract `RoopFloyd_Portable_v4.4.1.zip` to any folder
   - No installation required!

2. **Run the Application**
   - **Windows**: Double-click `Start_RoopFloyd.bat`
   - **Linux/Mac**: Run `./start_roopfloyd.sh` in terminal
   - **PowerShell**: Run `Start_RoopFloyd.ps1`

3. **First-Time Setup** (Automatic)
   - Python dependencies install automatically (~350 MB)
   - AI models download when first used (~2.3 GB)
   - Web interface opens in your browser

4. **Start FSping!**
   - Upload source face image
   - Upload target image/video
   - Click "Start" and wait for magic! ✨

## 📋 System Requirements

### Minimum:
- **Python 3.9+** (must be installed separately)
- **4 GB RAM**
- **3 GB free disk space**
- **Internet connection** (for first-time setup)

### Recommended:
- **Python 3.10+**
- **8 GB+ RAM**
- **NVIDIA GPU** with CUDA support
- **5 GB+ free disk space**
- **FFmpeg** (for video processing)

## 🛠 Installation Guide

### Step 1: Install Python
If Python isn't installed:
1. Download from [python.org](https://python.org)
2. **Important**: Check "Add Python to PATH" during installation
3. Verify: Open command prompt and type `python --version`

### Step 2: Install FFmpeg (Optional but Recommended)
For video processing:
1. Download from [ffmpeg.org](https://ffmpeg.org)
2. Add to system PATH
3. Verify: Type `ffmpeg -version` in command prompt

### Step 3: Run Roop Floyd
1. Extract the ZIP file
2. Run the appropriate launcher for your system
3. Wait for automatic setup to complete

## 🎯 Features

### Core Features:
- **FSping**: Replace faces in images and videos
- **Face Enhancement**: Improve face quality with AI
- **Batch Processing**: Process multiple files at once
- **Live Camera**: Real-time face swapping via webcam
- **Multiple Models**: Various AI models for different quality levels

### Supported Formats:
- **Images**: JPG, PNG, BMP, TIFF
- **Videos**: MP4, AVI, MOV, MKV, GIF
- **Output**: Customizable formats and quality

## 🔧 Troubleshooting

### Common Issues:

#### "Python not found"
- Install Python 3.9+ from python.org
- Ensure Python is added to system PATH
- Restart command prompt/terminal

#### "Dependencies failed to install"
- Check internet connection
- Run as administrator (Windows)
- Try CPU-only mode if GPU drivers are missing

#### "Models failed to download"
- Check internet connection
- Ensure sufficient disk space (3+ GB)
- Check firewall/antivirus settings

#### "FFmpeg not found"
- Install FFmpeg for video processing
- Add FFmpeg to system PATH
- Image processing will still work without FFmpeg

#### "Out of memory"
- Close other applications
- Use smaller input files
- Enable CPU-only mode in settings

### Performance Tips:

#### For Better Speed:
- Use NVIDIA GPU with CUDA
- Install GPU-enabled PyTorch
- Use smaller input resolutions
- Close unnecessary applications

#### For Compatibility:
- Use CPU-only mode
- Reduce memory usage in settings
- Use smaller batch sizes

## 📁 Folder Structure

```
RoopFloyd_Portable/
├── 📁 app/                    # Application files
│   ├── 🐍 launcher.py         # Main launcher
│   ├── 📁 roop/              # Face swap engine
│   ├── 📁 ui/                # Web interface
│   └── 📁 clip/              # AI components
├── 📁 models/                # AI models (auto-downloaded)
├── 📁 output/                # Your generated files
├── 📁 temp/                  # Temporary files
├── 🚀 Start_RoopFloyd.bat    # Windows launcher
├── 🚀 Start_RoopFloyd.ps1    # PowerShell launcher
├── 🚀 start_roopfloyd.sh     # Linux/Mac launcher
└── 📖 README.txt             # Quick reference
```

## ⚖️ Legal & Ethical Use

### ⚠️ Important Disclaimers:
- **Educational/Research Use Only**
- **Obtain Consent**: Only use images/videos with permission
- **No Malicious Use**: Don't create misleading or harmful content
- **Check Local Laws**: Ensure compliance with your jurisdiction
- **Respect Privacy**: Don't use without subject's consent

### Responsible AI Use:
- Use for creative, educational, or research purposes
- Always disclose when content is AI-generated
- Respect intellectual property rights
- Consider the impact on individuals and society

## 🆘 Support

### Getting Help:
1. **Check Console Output**: Error messages provide clues
2. **Verify Requirements**: Ensure Python and disk space
3. **Try CPU Mode**: If GPU issues occur
4. **Check Internet**: Required for downloads

### Common Solutions:
- Restart the application
- Clear temp folder
- Reinstall dependencies
- Check system resources

## 🎉 Success!

You now have a fully functional AI face swapping application that:
- ✅ **Lightweight**: Only 0.09 MB initial download
- ✅ **Portable**: No installation required
- ✅ **Smart**: Auto-installs what it needs
- ✅ **Cross-platform**: Works on Windows, Linux, Mac
- ✅ **Professional**: Web-based interface
- ✅ **Powerful**: State-of-the-art AI models

**Enjoy creating amazing face swaps responsibly!** 🎭✨
