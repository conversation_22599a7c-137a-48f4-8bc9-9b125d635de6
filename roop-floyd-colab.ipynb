{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"id": "b1-c1oAXiIzN"}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "OB-9x7rPjMWk", "outputId": "e5f3d470-253e-413c-cfe8-03ff374a7ba0"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["CUDA available: True\n"]}], "source": ["import torch\n", "print(\"CUDA available:\", torch.cuda.is_available())"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "sMVmUDr3iUsK", "outputId": "e98f4061-0626-414c-caae-db40ffa426c1"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Killing tunnel 127.0.0.1:7860 <> https://5fbf6dcac8e6f3fb71.gradio.live\n", "^C\n"]}], "source": ["import shutil\n", "import os\n", "from google.colab import drive\n", "\n", "# Check if the user wants to flush the existing repository folder\n", "flush_repo = input(\"Do you want to flush the existing repository and start fresh? (yes/no): \").strip().lower()\n", "\n", "if flush_repo == 'yes':\n", "     #Clear the repository folder to reset locally\n", "    print(\"Flushing the repository folder locally...\")\n", "    !rm -rf /content/ROOP-FLOYD\n", "\n", "    # Also check if Google Drive is mounted and remove the repo there\n", "    if os.path.exists('/content/drive'):\n", "        delete_drive_repo = input(\"Do you also want to delete the repository from Google Drive? (yes/no): \").strip().lower()\n", "        if delete_drive_repo == 'yes':\n", "            # Check if the folder exists in Google Drive and remove it\n", "            drive_repo_path = '/content/drive/MyDrive/ROOP-FLOYD'\n", "            if os.path.exists(drive_repo_path):\n", "                print(\"Deleting the repository from Google Drive...\")\n", "                shutil.rmtree(drive_repo_path)\n", "            else:\n", "                print(\"No repository found on Google Drive.\")\n", "else:\n", "    print(\"Keeping the existing repository folder.\")\n", "\n", "# Clone the repository and install dependencies (will redownload if flushed)\n", "!git clone https://codeberg.org/Cognibuild/ROOP-FLOYD.git\n", "%cd ROOP-FLOYD\n", "!pip install -r requirements.txt\n", "!pip install gradio==5.13.0 --upgrade\n", "!pip install --upgrade fastapi\n", "!pip install --force-reinstall pydantic==2.10.6\n", "!pip install \"numpy<2.0\"\n", "\n", "# Check if Google Drive is already mounted\n", "if not os.path.exists('/content/drive'):\n", "    # Prompt user to choose whether they want to save to Google Drive\n", "    save_to_drive = input(\"Do you want to save the repository to Google Drive? (yes/no): \").strip().lower()\n", "\n", "    if save_to_drive == 'yes':\n", "        drive.mount('/content/drive')\n", "\n", "        # Copy repository to Google Drive\n", "        !cp -r /content/ROOP-FLOYD /content/drive/MyDrive/ROOP-FLOYD\n", "        print(\"Repository saved to Google Drive.\")\n", "else:\n", "    print(\"Google Drive is already mounted.\")\n", "\n", "# Run the main script\n", "!python run.py\n"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}