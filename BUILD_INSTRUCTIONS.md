# Roop Floyd FS - Build Instructions

## Overview
This document provides instructions for creating executable and installable versions of the Roop Floyd FS application.

## Available Build Options

### 1. Portable Application (Recommended)
- Self-contained ZIP file
- No installation required
- Works on Windows, Linux, and macOS
- Automatically installs dependencies on first run

### 2. Windows Installer
- Professional MSI-style installer
- Creates Start Menu shortcuts
- Adds to Add/Remove Programs
- Requires NSIS to build

### 3. Standalone Executable
- Single executable file
- No Python installation required
- Larger file size (~500MB+)
- Uses PyInstaller

## Quick Start

### Build All Versions:
```bash
python build_app.py --all
```

### Build Specific Version:
```bash
# Portable only
python build_app.py --portable

# Installer only  
python build_app.py --installer

# Executable only
python build_app.py --executable
```

## Manual Build Steps

### Portable Application:
1. Run: `python create_portable_app.py`
2. This creates `RoopFloyd_Portable/` folder and ZIP file
3. Distribute the ZIP file to users

### Windows Installer:
1. Install NSIS from https://nsis.sourceforge.io/
2. Run: `makensis installer.nsi`
3. This creates `RoopFloyd_Setup_v4.4.1.exe`

### Standalone Executable:
1. Install PyInstaller: `pip install pyinstaller`
2. Run: `python build_app.py --executable`
3. Executable created in `dist/RoopFloyd/`

## User Instructions

### For Portable Version:
1. Extract ZIP file to desired location
2. **Windows**: Double-click `Start_RoopFloyd.bat`
3. **Linux/Mac**: Run `./start_roopfloyd.sh`
4. Dependencies install automatically on first run
5. Web interface opens in browser

### For Installed Version:
1. Run the installer executable
2. Follow installation wizard
3. Launch from Start Menu or desktop shortcut
4. Dependencies install automatically on first run

### For Standalone Executable:
1. Extract to desired location
2. Run `RoopFloyd.exe` (Windows) or `RoopFloyd` (Linux/Mac)
3. All dependencies included

## System Requirements

### Minimum:
- Python 3.9+ (for portable/installed versions)
- 4GB RAM
- 2GB free disk space
- Internet connection (for model downloads)

### Recommended:
- Python 3.10+
- 8GB+ RAM
- NVIDIA GPU with CUDA support
- 5GB+ free disk space
- FFmpeg installed (for video processing)

## Troubleshooting

### Common Issues:

1. **"Python not found"**
   - Install Python 3.9+ from python.org
   - Ensure Python is in system PATH

2. **"Dependencies failed to install"**
   - Check internet connection
   - Try running as administrator
   - Use CPU-only mode if GPU drivers missing

3. **"FFmpeg not found"**
   - Install FFmpeg from ffmpeg.org
   - Add to system PATH
   - Video processing will be limited without FFmpeg

4. **"Models failed to download"**
   - Check internet connection
   - Ensure sufficient disk space
   - Models are downloaded to `models/` folder

### Performance Tips:

1. **GPU Acceleration**:
   - Install CUDA toolkit for NVIDIA GPUs
   - Use GPU-enabled PyTorch/ONNX Runtime
   - Significantly faster processing

2. **Memory Usage**:
   - Close other applications
   - Adjust memory limit in settings
   - Use smaller input images/videos

3. **Processing Speed**:
   - Use CPU-only for compatibility
   - GPU processing is much faster
   - Batch processing for multiple files

## File Structure

```
RoopFloyd_Portable/
├── app/                    # Application source code
│   ├── roop/              # Core face swap engine
│   ├── ui/                # Web interface
│   ├── clip/              # AI model components
│   ├── launcher.py        # Application launcher
│   ├── run.py             # Main entry point
│   ├── settings.py        # Configuration
│   ├── config.yaml        # Default settings
│   └── requirements.txt   # Dependencies
├── models/                # AI models (downloaded)
├── output/                # Generated files
├── temp/                  # Temporary files
├── Start_RoopFloyd.bat    # Windows launcher
├── Start_RoopFloyd.ps1    # PowerShell launcher
├── start_roopfloyd.sh     # Linux/Mac launcher
└── README.txt             # User instructions
```

## Development Notes

### Key Components:
- **launcher.py**: Handles environment setup and dependency installation
- **run.py**: Main application entry point
- **roop/core.py**: Core face swapping logic
- **ui/main.py**: Gradio web interface
- **settings.py**: Configuration management

### Build Scripts:
- **build_app.py**: Master build script
- **create_portable_app.py**: Creates portable distribution
- **installer.nsi**: NSIS installer script

### Configuration:
- **config.yaml**: Default application settings
- **requirements.txt**: Python dependencies
- **requirements_portable.txt**: CPU-only dependencies

## Support

For issues or questions:
1. Check console output for error messages
2. Verify system requirements
3. Try CPU-only mode if GPU issues
4. Ensure sufficient disk space and internet connectivity

## License

MIT License - See LICENSE.txt for details.

**Disclaimer**: This software is for educational and research purposes only. Users are responsible for compliance with applicable laws regarding AI-generated content.
