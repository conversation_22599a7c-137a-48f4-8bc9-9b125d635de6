# Roop Floyd FS PowerShell Launcher
# Requires PowerShell 5.0 or higher

param(
    [switch]$NoVenv,
    [switch]$ForceReinstall
)

# Set console title
$Host.UI.RawUI.WindowTitle = "Roop Floyd FS Launcher"

# Application info
$AppName = "Roop Floyd FS"
$AppVersion = "4.4.1"

function Write-Banner {
    Write-Host "================================================================" -ForegroundColor Cyan
    Write-Host "  $AppName v$AppVersion" -ForegroundColor Yellow
    Write-Host "  AI-Powered FSping Application" -ForegroundColor Green
    Write-Host "================================================================" -ForegroundColor Cyan
    Write-Host ""
}

function Test-PythonInstallation {
    try {
        $pythonVersion = python --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Python version: $pythonVersion" -ForegroundColor Green
            return $true
        }
    }
    catch {
        Write-Host "Error: Python is not installed or not in PATH!" -ForegroundColor Red
        Write-Host "Please install Python 3.9 or higher from https://python.org" -ForegroundColor Yellow
        return $false
    }
    return $false
}

function Setup-VirtualEnvironment {
    if (-not (Test-Path "venv") -or $ForceReinstall) {
        Write-Host "Creating virtual environment..." -ForegroundColor Yellow
        python -m venv venv
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Failed to create virtual environment!" -ForegroundColor Red
            return $false
        }
    }
    
    Write-Host "Activating virtual environment..." -ForegroundColor Yellow
    & "venv\Scripts\Activate.ps1"
    return $true
}

function Install-Dependencies {
    Write-Host "Installing/updating dependencies..." -ForegroundColor Yellow
    pip install -r requirements.txt --quiet --upgrade
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Warning: Some dependencies may not have installed correctly." -ForegroundColor Yellow
    }
}

function Start-Application {
    Write-Host ""
    Write-Host "Starting $AppName..." -ForegroundColor Green
    Write-Host "The web interface will open in your browser." -ForegroundColor Cyan
    Write-Host "Press Ctrl+C to stop the application." -ForegroundColor Yellow
    Write-Host ""
    
    try {
        python launcher.py
    }
    catch {
        Write-Host "Error starting application: $_" -ForegroundColor Red
    }
}

# Main execution
try {
    # Set location to script directory
    Set-Location $PSScriptRoot
    
    Write-Banner
    
    # Check Python installation
    if (-not (Test-PythonInstallation)) {
        Read-Host "Press Enter to exit"
        exit 1
    }
    
    # Setup virtual environment (unless disabled)
    if (-not $NoVenv) {
        if (-not (Setup-VirtualEnvironment)) {
            Read-Host "Press Enter to exit"
            exit 1
        }
        Install-Dependencies
    }
    
    # Start the application
    Start-Application
}
catch {
    Write-Host "An error occurred: $_" -ForegroundColor Red
}
finally {
    Write-Host ""
    Write-Host "Application closed." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
}
