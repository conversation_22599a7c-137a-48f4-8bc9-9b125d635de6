{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "cells": [{"cell_type": "code", "execution_count": null, "metadata": {"id": "b1-c1oAXiIzN"}, "outputs": [], "source": []}, {"cell_type": "code", "source": ["import torch\n", "print(\"CUDA available:\", torch.cuda.is_available())"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "OB-9x7rPjMWk", "outputId": "9b1745aa-a18a-4219-be42-7d98e1854f73"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["CUDA available: True\n"]}]}, {"cell_type": "code", "source": ["import shutil\n", "import os\n", "from google.colab import drive\n", "\n", "# Check if the user wants to flush the existing repository folder\n", "flush_repo = input(\"Do you want to flush the existing repository and start fresh? (yes/no): \").strip().lower()\n", "\n", "if flush_repo == 'yes':\n", "    # Clear the repository folder to reset locally\n", "    print(\"Flushing the repository folder locally...\")\n", "    !rm -rf /content/ROOP-FLOYD\n", "\n", "    # Also check if Google Drive is mounted and remove the repo there\n", "    if os.path.exists('/content/drive'):\n", "        delete_drive_repo = input(\"Do you also want to delete the repository from Google Drive? (yes/no): \").strip().lower()\n", "        if delete_drive_repo == 'yes':\n", "            # Check if the folder exists in Google Drive and remove it\n", "            drive_repo_path = '/content/drive/MyDrive/ROOP-FLOYD'\n", "            if os.path.exists(drive_repo_path):\n", "                print(\"Deleting the repository from Google Drive...\")\n", "                shutil.rmtree(drive_repo_path)\n", "            else:\n", "                print(\"No repository found on Google Drive.\")\n", "else:\n", "    print(\"Keeping the existing repository folder.\")\n", "\n", "# Clone the repository and install dependencies (will redownload if flushed)\n", "!git clone https://codeberg.org/Cognibuild/ROOP-FLOYD.git\n", "%cd ROOP-FLOYD\n", "!pip install -r requirements.txt\n", "!pip install --upgrade gradio --force\n", "!pip install --upgrade fastapi pydantic\n", "!pip install \"numpy<2.0\"\n", "\n", "# Check if Google Drive is already mounted\n", "if not os.path.exists('/content/drive'):\n", "    # Prompt user to choose whether they want to save to Google Drive\n", "    save_to_drive = input(\"Do you want to save the repository to Google Drive? (yes/no): \").strip().lower()\n", "\n", "    if save_to_drive == 'yes':\n", "        drive.mount('/content/drive')\n", "\n", "        # Copy repository to Google Drive\n", "        !cp -r /content/ROOP-FLOYD /content/drive/MyDrive/ROOP-FLOYD\n", "        print(\"Repository saved to Google Drive.\")\n", "else:\n", "    print(\"Google Drive is already mounted.\")\n", "\n", "# Run the main script\n", "!python run.py\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "sMVmUDr3iUsK", "outputId": "dcada8d4-db91-41bd-a3ac-d4482664a511"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Do you want to flush the existing repository and start fresh? (yes/no): no\n", "Keeping the existing repository folder.\n", "fatal: destination path 'ROOP-FLOYD' already exists and is not an empty directory.\n", "/content/ROOP-FLOYD\n", "\u001b[33mWARNING: Ignoring invalid distribution ~ygments (/usr/local/lib/python3.11/dist-packages)\u001b[0m\u001b[33m\n", "\u001b[0m\u001b[33mWARNING: Ignoring invalid distribution ~ygments (/usr/local/lib/python3.11/dist-packages)\u001b[0m\u001b[33m\n", "\u001b[0mLooking in indexes: https://pypi.org/simple, https://download.pytorch.org/whl/cu124\n", "Ignoring torch: markers 'sys_platform == \"darwin\"' don't match your environment\n", "Ignoring torchvision: markers 'sys_platform == \"darwin\"' don't match your environment\n", "Ignoring onnxruntime: markers 'sys_platform == \"darwin\" and platform_machine != \"arm64\"' don't match your environment\n", "Ignoring onnxruntime-silicon: markers 'sys_platform == \"darwin\" and platform_machine == \"arm64\"' don't match your environment\n", "Requirement already satisfied: numpy==1.26.4 in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 2)) (1.26.4)\n", "Requirement already satisfied: gradio==5.9.1 in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 3)) (5.9.1)\n", "Requirement already satisfied: opencv-python-headless==4.10.0.84 in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 4)) (4.10.0.84)\n", "Requirement already satisfied: onnx==1.16.1 in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 5)) (1.16.1)\n", "Requirement already satisfied: insightface==0.7.3 in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 6)) (0.7.3)\n", "Requirement already satisfied: albucore==0.0.16 in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 7)) (0.0.16)\n", "Requirement already satisfied: psutil==5.9.6 in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 8)) (5.9.6)\n", "Requirement already satisfied: torch==2.5.1+cu124 in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 9)) (2.5.1+cu124)\n", "Requirement already satisfied: torchvision==0.20.1+cu124 in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 11)) (0.20.1+cu124)\n", "Requirement already satisfied: onnxruntime-gpu==1.20.1 in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 15)) (1.20.1)\n", "Requirement already satisfied: tqdm==4.66.4 in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 16)) (4.66.4)\n", "Requirement already satisfied: ftfy in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 17)) (6.3.1)\n", "Requirement already satisfied: regex in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 18)) (2024.11.6)\n", "Requirement already satisfied: pyvirtualcam in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 19)) (0.12.1)\n", "Requirement already satisfied: aiofiles<24.0,>=22.0 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (23.2.1)\n", "Requirement already satisfied: anyio<5.0,>=3.0 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (3.7.1)\n", "Requirement already satisfied: fastapi<1.0,>=0.115.2 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (0.115.8)\n", "Requirement already satisfied: ffmpy in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (0.5.0)\n", "Requirement already satisfied: gradio-client==1.5.2 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (1.5.2)\n", "Requirement already satisfied: httpx>=0.24.1 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (0.28.1)\n", "Requirement already satisfied: huggingface-hub>=0.25.1 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (0.27.1)\n", "Requirement already satisfied: jinja2<4.0 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (3.1.5)\n", "Requirement already satisfied: markupsafe~=2.0 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (2.1.5)\n", "Requirement already satisfied: orjson~=3.0 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (3.10.15)\n", "Requirement already satisfied: packaging in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (24.2)\n", "Requirement already satisfied: pandas<3.0,>=1.0 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (2.2.2)\n", "Requirement already satisfied: pillow<12.0,>=8.0 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (11.1.0)\n", "Requirement already satisfied: pydantic>=2.0 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (2.10.6)\n", "Requirement already satisfied: pydub in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (0.25.1)\n", "Requirement already satisfied: python-multipart>=0.0.18 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (0.0.20)\n", "Requirement already satisfied: pyyaml<7.0,>=5.0 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (6.0.2)\n", "Requirement already satisfied: ruff>=0.2.2 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (0.9.4)\n", "Requirement already satisfied: safehttpx<0.2.0,>=0.1.6 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (0.1.6)\n", "Requirement already satisfied: semantic-version~=2.0 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (2.10.0)\n", "Requirement already satisfied: starlette<1.0,>=0.40.0 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (0.45.3)\n", "Requirement already satisfied: tomlkit<0.14.0,>=0.12.0 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (0.13.2)\n", "Requirement already satisfied: typer<1.0,>=0.12 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (0.15.1)\n", "Requirement already satisfied: typing-extensions~=4.0 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (4.12.2)\n", "Requirement already satisfied: uvicorn>=0.14.0 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (0.34.0)\n", "Requirement already satisfied: protobuf>=3.20.2 in /usr/local/lib/python3.11/dist-packages (from onnx==1.16.1->-r requirements.txt (line 5)) (4.25.6)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.11/dist-packages (from insightface==0.7.3->-r requirements.txt (line 6)) (2.32.3)\n", "Requirement already satisfied: matplotlib in /usr/local/lib/python3.11/dist-packages (from insightface==0.7.3->-r requirements.txt (line 6)) (3.10.0)\n", "Requirement already satisfied: scipy in /usr/local/lib/python3.11/dist-packages (from insightface==0.7.3->-r requirements.txt (line 6)) (1.13.1)\n", "Requirement already satisfied: scikit-learn in /usr/local/lib/python3.11/dist-packages (from insightface==0.7.3->-r requirements.txt (line 6)) (1.6.1)\n", "Requirement already satisfied: scikit-image in /usr/local/lib/python3.11/dist-packages (from insightface==0.7.3->-r requirements.txt (line 6)) (0.25.1)\n", "Requirement already satisfied: easydict in /usr/local/lib/python3.11/dist-packages (from insightface==0.7.3->-r requirements.txt (line 6)) (1.13)\n", "Requirement already satisfied: cython in /usr/local/lib/python3.11/dist-packages (from insightface==0.7.3->-r requirements.txt (line 6)) (3.0.11)\n", "Requirement already satisfied: albumentations in /usr/local/lib/python3.11/dist-packages (from insightface==0.7.3->-r requirements.txt (line 6)) (1.4.15)\n", "Requirement already satisfied: prettytable in /usr/local/lib/python3.11/dist-packages (from insightface==0.7.3->-r requirements.txt (line 6)) (3.13.0)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (3.17.0)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (3.4.2)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (2024.10.0)\n", "Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (12.4.127)\n", "Requirement already satisfied: nvidia-cuda-runtime-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (12.4.127)\n", "Requirement already satisfied: nvidia-cuda-cupti-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (12.4.127)\n", "Requirement already satisfied: nvidia-cudnn-cu12==9.1.0.70 in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (9.1.0.70)\n", "Requirement already satisfied: nvidia-cublas-cu12==12.4.5.8 in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (12.4.5.8)\n", "Requirement already satisfied: nvidia-cufft-cu12==11.2.1.3 in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (11.2.1.3)\n", "Requirement already satisfied: nvidia-curand-cu12==10.3.5.147 in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (10.3.5.147)\n", "Requirement already satisfied: nvidia-cusolver-cu12==11.6.1.9 in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (11.6.1.9)\n", "Requirement already satisfied: nvidia-cusparse-cu12==12.3.1.170 in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (12.3.1.170)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.21.5 in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (2.21.5)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (12.4.127)\n", "Requirement already satisfied: nvidia-nvjitlink-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (12.4.127)\n", "Requirement already satisfied: triton==3.1.0 in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (3.1.0)\n", "Requirement already satisfied: sympy==1.13.1 in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (1.13.1)\n", "Requirement already satisfied: coloredlogs in /usr/local/lib/python3.11/dist-packages (from onnxruntime-gpu==1.20.1->-r requirements.txt (line 15)) (15.0.1)\n", "Requirement already satisfied: flatbuffers in /usr/local/lib/python3.11/dist-packages (from onnxruntime-gpu==1.20.1->-r requirements.txt (line 15)) (25.1.24)\n", "Requirement already satisfied: websockets<15.0,>=10.0 in /usr/local/lib/python3.11/dist-packages (from gradio-client==1.5.2->gradio==5.9.1->-r requirements.txt (line 3)) (14.2)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy==1.13.1->torch==2.5.1+cu124->-r requirements.txt (line 9)) (1.3.0)\n", "Requirement already satisfied: wcwidth in /usr/local/lib/python3.11/dist-packages (from ftfy->-r requirements.txt (line 17)) (0.2.13)\n", "Requirement already satisfied: idna>=2.8 in /usr/local/lib/python3.11/dist-packages (from anyio<5.0,>=3.0->gradio==5.9.1->-r requirements.txt (line 3)) (3.10)\n", "Requirement already satisfied: sniffio>=1.1 in /usr/local/lib/python3.11/dist-packages (from anyio<5.0,>=3.0->gradio==5.9.1->-r requirements.txt (line 3)) (1.3.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.11/dist-packages (from httpx>=0.24.1->gradio==5.9.1->-r requirements.txt (line 3)) (2024.12.14)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.11/dist-packages (from httpx>=0.24.1->gradio==5.9.1->-r requirements.txt (line 3)) (1.0.7)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.11/dist-packages (from httpcore==1.*->httpx>=0.24.1->gradio==5.9.1->-r requirements.txt (line 3)) (0.14.0)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.11/dist-packages (from pandas<3.0,>=1.0->gradio==5.9.1->-r requirements.txt (line 3)) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas<3.0,>=1.0->gradio==5.9.1->-r requirements.txt (line 3)) (2025.1)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas<3.0,>=1.0->gradio==5.9.1->-r requirements.txt (line 3)) (2025.1)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic>=2.0->gradio==5.9.1->-r requirements.txt (line 3)) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in /usr/local/lib/python3.11/dist-packages (from pydantic>=2.0->gradio==5.9.1->-r requirements.txt (line 3)) (2.27.2)\n", "Requirement already satisfied: click>=8.0.0 in /usr/local/lib/python3.11/dist-packages (from typer<1.0,>=0.12->gradio==5.9.1->-r requirements.txt (line 3)) (8.1.8)\n", "Requirement already satisfied: shellingham>=1.3.0 in /usr/local/lib/python3.11/dist-packages (from typer<1.0,>=0.12->gradio==5.9.1->-r requirements.txt (line 3)) (1.5.4)\n", "Requirement already satisfied: rich>=10.11.0 in /usr/local/lib/python3.11/dist-packages (from typer<1.0,>=0.12->gradio==5.9.1->-r requirements.txt (line 3)) (13.9.4)\n", "Requirement already satisfied: eval-type-backport in /usr/local/lib/python3.11/dist-packages (from albumentations->insightface==0.7.3->-r requirements.txt (line 6)) (0.2.2)\n", "Requirement already satisfied: imageio!=2.35.0,>=2.33 in /usr/local/lib/python3.11/dist-packages (from scikit-image->insightface==0.7.3->-r requirements.txt (line 6)) (2.36.1)\n", "Requirement already satisfied: tifffile>=2022.8.12 in /usr/local/lib/python3.11/dist-packages (from scikit-image->insightface==0.7.3->-r requirements.txt (line 6)) (2025.1.10)\n", "Requirement already satisfied: lazy-loader>=0.4 in /usr/local/lib/python3.11/dist-packages (from scikit-image->insightface==0.7.3->-r requirements.txt (line 6)) (0.4)\n", "Requirement already satisfied: humanfriendly>=9.1 in /usr/local/lib/python3.11/dist-packages (from coloredlogs->onnxruntime-gpu==1.20.1->-r requirements.txt (line 15)) (10.0)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib->insightface==0.7.3->-r requirements.txt (line 6)) (1.3.1)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.11/dist-packages (from matplotlib->insightface==0.7.3->-r requirements.txt (line 6)) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib->insightface==0.7.3->-r requirements.txt (line 6)) (4.55.7)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib->insightface==0.7.3->-r requirements.txt (line 6)) (1.4.8)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib->insightface==0.7.3->-r requirements.txt (line 6)) (3.2.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests->insightface==0.7.3->-r requirements.txt (line 6)) (3.4.1)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests->insightface==0.7.3->-r requirements.txt (line 6)) (2.3.0)\n", "Requirement already satisfied: joblib>=1.2.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn->insightface==0.7.3->-r requirements.txt (line 6)) (1.4.2)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn->insightface==0.7.3->-r requirements.txt (line 6)) (3.5.0)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil>=2.8.2->pandas<3.0,>=1.0->gradio==5.9.1->-r requirements.txt (line 3)) (1.17.0)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in /usr/local/lib/python3.11/dist-packages (from rich>=10.11.0->typer<1.0,>=0.12->gradio==5.9.1->-r requirements.txt (line 3)) (3.0.0)\n", "Collecting pygments<3.0.0,>=2.13.0 (from rich>=10.11.0->typer<1.0,>=0.12->gradio==5.9.1->-r requirements.txt (line 3))\n", "  Using cached pygments-2.19.1-py3-none-any.whl.metadata (2.5 kB)\n", "Requirement already satisfied: mdurl~=0.1 in /usr/local/lib/python3.11/dist-packages (from markdown-it-py>=2.2.0->rich>=10.11.0->typer<1.0,>=0.12->gradio==5.9.1->-r requirements.txt (line 3)) (0.1.2)\n", "Using cached pygments-2.19.1-py3-none-any.whl (1.2 MB)\n", "\u001b[33mWARNING: Ignoring invalid distribution ~ygments (/usr/local/lib/python3.11/dist-packages)\u001b[0m\u001b[33m\n", "\u001b[0mInstalling collected packages: pygments\n", "\u001b[33mWARNING: Ignoring invalid distribution ~ygments (/usr/local/lib/python3.11/dist-packages)\u001b[0m\u001b[33m\n", "\u001b[0m\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "ipython 7.34.0 requires jedi>=0.16, which is not installed.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed pygments\n", "\u001b[33mWARNING: Ignoring invalid distribution ~ygments (/usr/local/lib/python3.11/dist-packages)\u001b[0m\u001b[33m\n", "\u001b[0m\u001b[33mWARNING: Ignoring invalid distribution ~ygments (/usr/local/lib/python3.11/dist-packages)\u001b[0m\u001b[33m\n", "\u001b[0mCollecting gradio\n", "  Using cached gradio-5.14.0-py3-none-any.whl.metadata (16 kB)\n", "Collecting aiofiles<24.0,>=22.0 (from gradio)\n", "  Using cached aiofiles-23.2.1-py3-none-any.whl.metadata (9.7 kB)\n", "Collecting anyio<5.0,>=3.0 (from gradio)\n", "  Using cached anyio-4.8.0-py3-none-any.whl.metadata (4.6 kB)\n", "Collecting fastapi<1.0,>=0.115.2 (from gradio)\n", "  Using cached fastapi-0.115.8-py3-none-any.whl.metadata (27 kB)\n", "Collecting ffmpy (from gradio)\n", "  Using cached ffmpy-0.5.0-py3-none-any.whl.metadata (3.0 kB)\n", "Collecting gradio-client==1.7.0 (from gradio)\n", "  Using cached gradio_client-1.7.0-py3-none-any.whl.metadata (7.1 kB)\n", "Collecting httpx>=0.24.1 (from gradio)\n", "  Using cached httpx-0.28.1-py3-none-any.whl.metadata (7.1 kB)\n", "Collecting huggingface-hub>=0.25.1 (from gradio)\n", "  Using cached huggingface_hub-0.28.1-py3-none-any.whl.metadata (13 kB)\n", "Collecting jinja2<4.0 (from gradio)\n", "  Using cached jinja2-3.1.5-py3-none-any.whl.metadata (2.6 kB)\n", "Collecting markupsafe~=2.0 (from gradio)\n", "  Using cached MarkupSafe-2.1.5-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (3.0 kB)\n", "Collecting numpy<3.0,>=1.0 (from gradio)\n", "  Using cached numpy-2.2.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (62 kB)\n", "Collecting orjson~=3.0 (from gradio)\n", "  Using cached orjson-3.10.15-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (41 kB)\n", "Collecting packaging (from gradio)\n", "  Using cached packaging-24.2-py3-none-any.whl.metadata (3.2 kB)\n", "Collecting pandas<3.0,>=1.0 (from gradio)\n", "  Using cached pandas-2.2.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (89 kB)\n", "Collecting pillow<12.0,>=8.0 (from gradio)\n", "  Using cached pillow-11.1.0-cp311-cp311-manylinux_2_28_x86_64.whl.metadata (9.1 kB)\n", "Collecting pydantic>=2.0 (from gradio)\n", "  Using cached pydantic-2.10.6-py3-none-any.whl.metadata (30 kB)\n", "Collecting pydub (from gradio)\n", "  Using cached pydub-0.25.1-py2.py3-none-any.whl.metadata (1.4 kB)\n", "Collecting python-multipart>=0.0.18 (from gradio)\n", "  Using cached python_multipart-0.0.20-py3-none-any.whl.metadata (1.8 kB)\n", "Collecting pyyaml<7.0,>=5.0 (from gradio)\n", "  Using cached PyYAML-6.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (2.1 kB)\n", "Collecting ruff>=0.9.3 (from gradio)\n", "  Using cached ruff-0.9.4-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (25 kB)\n", "Collecting safehttpx<0.2.0,>=0.1.6 (from gradio)\n", "  Using cached safehttpx-0.1.6-py3-none-any.whl.metadata (4.2 kB)\n", "Collecting semantic-version~=2.0 (from gradio)\n", "  Using cached semantic_version-2.10.0-py2.py3-none-any.whl.metadata (9.7 kB)\n", "Collecting starlette<1.0,>=0.40.0 (from gradio)\n", "  Using cached starlette-0.45.3-py3-none-any.whl.metadata (6.3 kB)\n", "Collecting tomlkit<0.14.0,>=0.12.0 (from gradio)\n", "  Using cached tomlkit-0.13.2-py3-none-any.whl.metadata (2.7 kB)\n", "Collecting typer<1.0,>=0.12 (from gradio)\n", "  Using cached typer-0.15.1-py3-none-any.whl.metadata (15 kB)\n", "Collecting typing-extensions~=4.0 (from gradio)\n", "  Using cached typing_extensions-4.12.2-py3-none-any.whl.metadata (3.0 kB)\n", "Collecting uvicorn>=0.14.0 (from gradio)\n", "  Using cached uvicorn-0.34.0-py3-none-any.whl.metadata (6.5 kB)\n", "Collecting fsspec (from gradio-client==1.7.0->gradio)\n", "  Using cached fsspec-2024.12.0-py3-none-any.whl.metadata (11 kB)\n", "Collecting websockets<15.0,>=10.0 (from gradio-client==1.7.0->gradio)\n", "  Using cached websockets-14.2-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.8 kB)\n", "Collecting idna>=2.8 (from anyio<5.0,>=3.0->gradio)\n", "  Using cached idna-3.10-py3-none-any.whl.metadata (10 kB)\n", "Collecting sniffio>=1.1 (from anyio<5.0,>=3.0->gradio)\n", "  Using cached sniffio-1.3.1-py3-none-any.whl.metadata (3.9 kB)\n", "Collecting certifi (from httpx>=0.24.1->gradio)\n", "  Using cached certifi-2025.1.31-py3-none-any.whl.metadata (2.5 kB)\n", "Collecting httpcore==1.* (from httpx>=0.24.1->gradio)\n", "  Using cached httpcore-1.0.7-py3-none-any.whl.metadata (21 kB)\n", "Collecting h11<0.15,>=0.13 (from httpcore==1.*->httpx>=0.24.1->gradio)\n", "  Using cached h11-0.14.0-py3-none-any.whl.metadata (8.2 kB)\n", "Collecting filelock (from huggingface-hub>=0.25.1->gradio)\n", "  Using cached filelock-3.17.0-py3-none-any.whl.metadata (2.9 kB)\n", "Collecting requests (from huggingface-hub>=0.25.1->gradio)\n", "  Using cached requests-2.32.3-py3-none-any.whl.metadata (4.6 kB)\n", "Collecting tqdm>=4.42.1 (from huggingface-hub>=0.25.1->gradio)\n", "  Using cached tqdm-4.67.1-py3-none-any.whl.metadata (57 kB)\n", "Collecting python-dateutil>=2.8.2 (from pandas<3.0,>=1.0->gradio)\n", "  Using cached python_dateutil-2.9.0.post0-py2.py3-none-any.whl.metadata (8.4 kB)\n", "Collecting pytz>=2020.1 (from pandas<3.0,>=1.0->gradio)\n", "  Using cached pytz-2025.1-py2.py3-none-any.whl.metadata (22 kB)\n", "Collecting tzdata>=2022.7 (from pandas<3.0,>=1.0->gradio)\n", "  Using cached tzdata-2025.1-py2.py3-none-any.whl.metadata (1.4 kB)\n", "Collecting annotated-types>=0.6.0 (from pydantic>=2.0->gradio)\n", "  Using cached annotated_types-0.7.0-py3-none-any.whl.metadata (15 kB)\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "2aQsEB8eic90"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "7F_0v598jKRJ"}, "execution_count": null, "outputs": []}]}