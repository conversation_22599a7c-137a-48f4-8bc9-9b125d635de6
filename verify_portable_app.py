#!/usr/bin/env python3
"""
Verification script for the portable app
Tests the structure and basic functionality
"""

import os
import sys
import zipfile
from pathlib import Path

def verify_zip_contents():
    """Verify the ZIP file contains all required files"""
    zip_path = "RoopFloyd_Portable_v4.4.1.zip"
    
    if not os.path.exists(zip_path):
        print("❌ ZIP file not found!")
        return False
    
    print(f"✅ ZIP file found: {zip_path}")
    
    # Check ZIP contents
    required_files = [
        "RoopFloyd_Portable/README.txt",
        "RoopFloyd_Portable/Start_RoopFloyd.bat",
        "RoopFloyd_Portable/Start_RoopFloyd.ps1", 
        "RoopFloyd_Portable/start_roopfloyd.sh",
        "RoopFloyd_Portable/app/launcher.py",
        "RoopFloyd_Portable/app/run.py",
        "RoopFloyd_Portable/app/settings.py",
        "RoopFloyd_Portable/app/config.yaml",
        "RoopFloyd_Portable/app/requirements_portable.txt",
        "RoopFloyd_Portable/app/roop/core.py",
        "RoopFloyd_Portable/app/ui/main.py"
    ]
    
    with zipfile.ZipFile(zip_path, 'r') as zip_file:
        zip_contents = zip_file.namelist()
        
        missing_files = []
        for required_file in required_files:
            if required_file not in zip_contents:
                missing_files.append(required_file)
        
        if missing_files:
            print("❌ Missing required files:")
            for file in missing_files:
                print(f"   - {file}")
            return False
        else:
            print("✅ All required files present in ZIP")
    
    return True

def verify_folder_structure():
    """Verify the extracted folder structure"""
    app_dir = Path("RoopFloyd_Portable")
    
    if not app_dir.exists():
        print("❌ Portable app folder not found!")
        return False
    
    print("✅ Portable app folder found")
    
    # Check required directories
    required_dirs = [
        "app",
        "app/roop", 
        "app/ui",
        "app/clip",
        "models",
        "output", 
        "temp"
    ]
    
    missing_dirs = []
    for dir_path in required_dirs:
        if not (app_dir / dir_path).exists():
            missing_dirs.append(dir_path)
    
    if missing_dirs:
        print("❌ Missing directories:")
        for dir_path in missing_dirs:
            print(f"   - {dir_path}")
        return False
    else:
        print("✅ All required directories present")
    
    return True

def verify_launcher_syntax():
    """Verify launcher script syntax"""
    launcher_path = Path("RoopFloyd_Portable/app/launcher.py")
    
    if not launcher_path.exists():
        print("❌ Launcher script not found!")
        return False
    
    try:
        with open(launcher_path, 'r') as f:
            code = f.read()
        
        # Basic syntax check
        compile(code, str(launcher_path), 'exec')
        print("✅ Launcher script syntax is valid")
        return True
    
    except SyntaxError as e:
        print(f"❌ Launcher script syntax error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error checking launcher script: {e}")
        return False

def get_file_sizes():
    """Get file sizes for reporting"""
    zip_path = Path("RoopFloyd_Portable_v4.4.1.zip")
    folder_path = Path("RoopFloyd_Portable")
    
    sizes = {}
    
    if zip_path.exists():
        sizes['zip_mb'] = zip_path.stat().st_size / (1024 * 1024)
    
    if folder_path.exists():
        total_size = sum(f.stat().st_size for f in folder_path.rglob('*') if f.is_file())
        sizes['folder_mb'] = total_size / (1024 * 1024)
    
    return sizes

def main():
    """Main verification function"""
    print("=" * 60)
    print("  Roop Floyd Portable App Verification")
    print("=" * 60)
    print()
    
    all_checks_passed = True
    
    # Verify ZIP contents
    if not verify_zip_contents():
        all_checks_passed = False
    print()
    
    # Verify folder structure
    if not verify_folder_structure():
        all_checks_passed = False
    print()
    
    # Verify launcher syntax
    if not verify_launcher_syntax():
        all_checks_passed = False
    print()
    
    # Get file sizes
    sizes = get_file_sizes()
    print("📊 File Sizes:")
    if 'zip_mb' in sizes:
        print(f"   ZIP file: {sizes['zip_mb']:.2f} MB")
    if 'folder_mb' in sizes:
        print(f"   Extracted folder: {sizes['folder_mb']:.2f} MB")
    print()
    
    # Final result
    if all_checks_passed:
        print("🎉 All verification checks passed!")
        print("✅ Portable app is ready for distribution")
        print()
        print("📦 Distribution Instructions:")
        print("1. Share the RoopFloyd_Portable_v4.4.1.zip file")
        print("2. Users extract to desired location")
        print("3. Users run Start_RoopFloyd.bat (Windows) or start_roopfloyd.sh (Linux/Mac)")
        print("4. Dependencies install automatically on first run")
        print("5. AI models download automatically when needed")
    else:
        print("❌ Some verification checks failed!")
        print("Please review the errors above and fix them.")
    
    print("=" * 60)
    return 0 if all_checks_passed else 1

if __name__ == "__main__":
    sys.exit(main())
